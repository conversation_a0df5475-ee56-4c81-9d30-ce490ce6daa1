package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DayWiseSlotWiseSalesDataDto {

    private Integer id;
    private Date cafeOpeningTime;
    private Date cafeClosingTime;
    private String isOperational;
    private Integer totalCafeOpeningTimeInMinutesForThatDay;
    private String productName;
    private Integer productType;
    private String inventoryTrackLevel;
    private Date businessDate;
    private Integer unitId;
    private Integer brandId;
    private Integer productId;
    private String dimension;
    private BigDecimal totalSaleAmount;
    private BigDecimal price;
    private BigDecimal totalStockOutInMinutesForThatDay;
    private BigDecimal productInStockTime;
    private BigDecimal orderLevelIncidence;
    private BigDecimal productItemLevelIncidence;
    private BigDecimal totalQuantitySold;
    private BigDecimal totalQuantitySoldDineIn;
    private BigDecimal totalQuantitySoldDelivery;
    private Integer totalOrders;
    private BigDecimal totalItemsSold;
    private BigDecimal totalDineInItemsSold;
    private BigDecimal totalDeliveryInItemsSold;
    private BigDecimal newCustomerInterest;
    private BigDecimal oldCustomerInterest;
    private Integer countOfOldCustomers;
    private Integer countOfNewCustomers;
    private Integer breakfastSales;
    private Integer lunchSales;
    private Integer eveningSales;
    private Integer dinnerSales;
    private Integer postDinnerSales;
    private Integer overnightSales;
    private BigDecimal breakfastSaleAmount;
    private BigDecimal lunchSaleAmount;
    private BigDecimal eveningSaleAmount;
    private BigDecimal dinnerSaleAmount;
    private BigDecimal postDinnerSaleAmount;
    private BigDecimal overnightSaleAmount;
    private Integer breakfastOrders;
    private Integer lunchOrders;
    private Integer eveningOrders;
    private Integer dinnerOrders;
    private Integer postDinnerOrders;
    private Integer overnightOrders;
    private Integer breakfastStockTime;
    private Integer lunchStockTime;
    private Integer eveningStockTime;
    private Integer dinnerStockTime;
    private Integer postDinnerStockTime;
    private Integer overnightStockTime;
    private Integer breakfastStockOutMinutes;
    private Integer lunchStockOutMinutes;
    private Integer eveningStockOutMinutes;
    private Integer dinnerStockOutMinutes;
    private Integer postDinnerStockOutMinutes;
    private Integer overnightStockOutMinutes;
    private Integer breakfastTimeInMinutes;
    private Integer lunchTimeInMinutes;
    private Integer eveningTimeInMinutes;
    private Integer dinnerTimeInMinutes;
    private Integer postDinnerTimeInMinutes;
    private Integer overnightTimeInMinutes;
    private String dayName;
    
    // Adjusted sales columns
    private BigDecimal adjustedTotalQuantitySold;
    private Integer adjustedBreakfastSales;
    private Integer adjustedLunchSales;
    private Integer adjustedEveningSales;
    private Integer adjustedDinnerSales;
    private Integer adjustedPostDinnerSales;
    private Integer adjustedOvernightSales;
}
