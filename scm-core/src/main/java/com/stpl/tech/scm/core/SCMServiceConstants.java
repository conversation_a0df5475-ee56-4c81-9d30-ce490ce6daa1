/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.core;

import java.math.BigDecimal;

/**
 * Created by <PERSON><PERSON> on 04-05-2016.
 */
public class SCMServiceConstants {

	public static final String API_VERSION = "v1";

	public static final String SEPARATOR = "/";
	public static final String FULLFILLMENT_REPORT_CONTEXT = "fullfillment-report";
	public static final String PRINTER_ROOT_CONTEXT = "printer";

	public static final String SCM_METADATA_ROOT_CONTEXT = "scm-metadata";

	public static final String SCM_DATA_ROOT_CONTEXT = "scm-data";

	public static final String ATTRIBUTE_MANAGEMENT_ROOT_CONTEXT = "attribute-management";

	public static final String UNIT_MANAGEMENT_ROOT_CONTEXT = "unit-management";

	public static final String PRODUCT_MANAGEMENT_ROOT_CONTEXT = "product-management";

	public static final String PROFILE_MANAGEMENT_ROOT_CONTEXT = "profile-management";

	public static final String ASSET_MANAGEMENT_ROOT_CONTEXT = "asset-management";

	public static final String CATEGORY_MANAGEMENT_ROOT_CONTEXT = "category-management";

	public static final String SKU_MAPPING_MANAGEMENT_ROOT_CONTEXT = "sku-mapping-management";
	public static final String VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT = "vendor-contract-management";

	public static final String REQUEST_ORDER_MANAGEMENT_ROOT_CONTEXT = "request-order-management";

	public static final String AUTOMATED_SCM_REPORTS_RESOURCE = "automated-scm-reports-management";

	public static final String REFERENCE_ORDER_MANAGEMENT_ROOT_CONTEXT = "reference-order-management";

	public static final String TRANSFER_ORDER_MANAGEMENT_ROOT_CONTEXT = "transfer-order-management";

	public static final String TRANSPORT_MANAGEMENT_ROOT_CONTEXT = "transport-management";

	public static final String PURCHASE_ORDER_MANAGEMENT_ROOT_CONTEXT = "purchase-order-management";

	public static final String GOODS_RECEIVED_MANAGEMENT_ROOT_CONTEXT = "goods-receive-management";

	public static final String PRICE_MANAGEMENT_ROOT_CONTEXT = "price-management";

	public static final String CACHE_MANAGEMENT_ROOT_CONTEXT = "cache-management";

	public static final String NOTIFICATION_FALLBACK_ROOT_CONTEXT = "notification-fallback";

	public static final String STOCK_MANAGEMENT_ROOT_CONTEXT = "stock-management";
	public static final String MONK_DAY_CLOSE_MANAGEMENT_ROOT_CONTEXT = "monk-day-close";

	public static final String WAREHOUSE_STOCK_MANAGEMENT_ROOT_CONTEXT = "wh-stock-management";

	public static final String VENDOR_MANAGEMENT_ROOT_CONTEXT = "vendor-management";

	public static final String VENDOR_REGISTRATION_ROOT_CONTEXT = "vendor-registration-management";

	public static final String PRODUCTION_BOOKING_ROOT_CONTEXT = "production-booking";

	public static final String PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT = "payment-request-management";

	public static final String GENERIC_RESOURCE_MANAGEMENT_ROOT_CONTEXT = "generic-resource-management";

	public static final String INVOICE_MANAGEMENT_ROOT_CONTEXT = "invoice-management";

	public static final String GATEPASS_MANAGEMENT_ROOT_CONTEXT = "gatepass-management";

	public static final String DATA_FILTER_ROOT_CONTEXT = "filter";

	public static final String SERVICE_ORDER_MANAGEMENT_ROOT_CONTEXT = "service-order-management";

	public static final String SERVICE_RECEIVE_MANAGEMENT_ROOT_CONTEXT = "service-receive-management";

	public static final String SERVICE_MAPPING_MANAGEMENT_ROOT_CONTEXT = "service-mapping-management";

	public static final String VEHICLE_MANAGEMENT = "vehicle-management";

	public static final String CAPEX_MANAGEMENT_ROOT_CONTEXT = "capex-management";

	public static final String PRODUCT_PROJECTIONS_ROOT_CONTEXT = "product-projections";
	public static final String SWITCH_ASSET_ROOT_CONTEXT = "switch-asset";
	public static final String LDC_VENDOR_ROOT_CONTEXT = "ldc-vendor";

	public static final String STOCK_REDISTRIBUTION_ROOT_CONTEXT = "stock-redistribution";
	public static final String MONK_WASTAGE_ROOT_CONTEXT = "monk-wastage-management";
	public static final String KETTLE_STOCK_OUT_ROOT_CONTEXT = "kettle-stock-out";

	public static final String SCM_CONSTANT_YES = "Y";

	public static final String SCM_CONSTANT_NO = "N";

	public static final String CHARSET = "utf-8";

	public static final int SYSTEM_USER = 120056;

	public static final  String SYSTEM_USER_EXPIRY_REASON = "AUTO GENERATED";

	public static final int CATEGORY_COGS = 1;
	public static final int CATEGORY_SEMI_FINISHED = 4;
	public static final int CATEGORY_CONSUMABLE = 2;
	public static final int CATEGORY_FIXED_ASSETS = 3;

	public static final int CASH_PURCHASE_VENDOR_ID = 22;

	// Below mentioned constants for calculating list type to be presented for Stock
	// Take
	public static final String PHASE_2_ROLLOUT_DATE = "2017-06-26";

	public static final String IGST = "IGST";

	public static final String CGST = "CGST";

	public static final String SGST = "SGST";

	public static final int CAFE = 1;

	public static final int WAREHOUSE = 3;

	public static final int KITCHEN = 4;

	public static final String WASTAGE_TYPE_UNSATISFIED_CUSTOMER = "UnsatisfiedCustomer";
	public static final String WASTAGE_TYPE_PPE = "PPE";
	public static final String WASTAGE_TYPE_EXPIRY = "Expired";
	public static final String WASTAGE_TYPE_SAMPLING_AND_MARKETING = "SamplingNMarketing";
	public static final String WASTAGE_TYPE_TRAINING = "Training";

	public static final int SUB_CATEGORY_CUTLERY = 10;
	public static final int SUB_CATEGORY_EQUIPMENT = 11;
	public static final int SUB_CATEGORY_STATIONERY = 12;
	public static final int SUB_CATEGORY_UNIFORM = 13;
	public static final int SUB_CATEGORY_UTILITY = 14;
	public static final int SUB_CATEGORY_MARKETING = 15;
	public static final int SUB_CATEGORY_DISPOSABLE = 4;
	public static final int SUB_CATEGORY_MAINTENANCE = 22;
	public static final int SUB_CATEGORY_CONSUM_KITCHEN_EQUIPMENT= 23;
	public static final int SUB_CATEGORY_CONSUM_IT = 24;
	public static final int SUB_CATEGORY_CONSUM_OFFICE_EQUIPMENT= 25;
	public static final int SUB_CATEGORY_CHAI_MONK= 26;
	public static final int SUB_CATEGORY_CONSUM_LHI= 27;

	public static final BigDecimal FA_PRICE_THRESHOLD = new BigDecimal("5000.00");

	public static final int SUB_CATEGORY_FA_EQUIPMENT = -1;

	public static final int SUB_CATEGORY_FA_KITCHEN = 16;
	public static final int SUB_CATEGORY_FA_IT = 17;
	public static final int SUB_CATEGORY_FA_OFFICE = 18;
	public static final int SUB_CATEGORY_FA_FURNITURE = 19;


	public static final int SUB_CATEGORY_FA_VEHICLE = 28;

	public static final BigDecimal CAPEX_BUDGET_CONCESSION = new BigDecimal("5.00");

	public static final BigDecimal WASTAGE_LIMIT = new BigDecimal("1000.00");

	public static final String SUNSHINE_TEAHOUSE = "SUNSHINE TEAHOUSE PVT LTD.";
	public static final Integer DOHFUL_COMPANY_ID = 1005;
	public static final Integer CHAAYOS_COMPANY_ID = 1000;
	public static final String DOHFUL_BRAND_NAME = "Dohful";
	public static final String DOHFUL_COMPANY_NAME = "Grubcha Foods Private Limited";

	public static final String PRODUCTION_LINE="Regular";

	public static final String STANDALONE_BULK_TRANSFER = "STANDALONE";

	public static final String REGULAR_BULK_TRANSFER = "REGULAR";
	public static final String UNIT ="UNIT";
	public static final String EMPLOYEE ="EMPLOYEE";

	public static final String STAND_ALONE_ADVANCE ="STAND_ALONE_ADVANCE";
	public static final String PO_ADVANCE ="PO_ADVANCE";
	public static final String SO_ADVANCE ="SO_ADVANCE";
	public static final String YES ="YES";
	public static final String NO ="NO";

	public static final String EXPIRED = "Expired";
    public static final String CHAAYOS_BIG_DAY = "CHAAYOS_BIG_DAY";
	public static final String AUTO_DAY_CLOSE = "AUTO_DAY_CLOSE";
	public static final String SECTION_206_AB_CHECK_REQUEST = "in.co.sandbox.tds.compliance.206ab_check.request";
	public static final String SECTION_206_REASON = "VALIDATING SECTION 206AB & 206CCA";

	public static final String CATEGORY_LEVEL_1 = "LEVEL_1";
	public static final String TECHNOLOGY_EMAIL = "<EMAIL>";
	public static final String FINANCE_EMAIL = "<EMAIL>";
	public static final String REPORTING_EMAIL = "<EMAIL>";
	public static final String SUPPLY_CHAIN_EMAIL = "<EMAIL>";
	public static final String PRODUCT_UPDATE_EMAIL = "<EMAIL>";
	public static final String VARIANCE_EMAIL = "<EMAIL>";

	public static final String GNT_PRODUCT_TYPE = "GNT";
	public static final Integer SYSTEM_USER_2 = 140199;
	public static final Integer KAPIL_GOLA_USER_ID = 141530;
	public static final String STOCK_REDISTRIBUTION = "STOCK_REDISTRIBUTION";

	public static final String ACK_RO_IN_TRANSIT = "ACK_RO_IN_TRANSIT";

	public static final String STRATEGY_MEAN_METADATA_STATUS_ACTIVE="ACTIVE";

	public static final BigDecimal MONK_WASTAGE_THRESHOLD = BigDecimal.valueOf(0.5); // IN LITERS

	public static final String MONK_WASTAGE = "Monk Wastage";
}
