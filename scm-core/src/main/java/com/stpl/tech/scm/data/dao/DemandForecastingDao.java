/*
 * Created By Shanmu<PERSON>
 */

package com.stpl.tech.scm.data.dao;

import com.stpl.tech.scm.data.model.DayWiseSlotWiseSalesData;
import com.stpl.tech.scm.data.model.HolidaysListData;
import com.stpl.tech.scm.data.model.UnitWiseOrderingStrategyData;

import java.util.List;
import java.util.Date;

public interface DemandForecastingDao {

    UnitWiseOrderingStrategyData getUnitWiseOrderingStrategyDataByUnitId(Integer unitId);

    List<DayWiseSlotWiseSalesData> getHistoricalSalesData(Integer unitId, Integer brandId, Date startDate, Date endDate);

    List<HolidaysListData> getHolidayListOfType(Date startDate, Date endDate, List<String> holidayTypes);

}
