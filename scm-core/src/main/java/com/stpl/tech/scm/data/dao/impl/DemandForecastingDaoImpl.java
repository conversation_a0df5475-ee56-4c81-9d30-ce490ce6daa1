/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.data.dao.impl;

import com.stpl.tech.scm.data.dao.DemandForecastingDao;
import com.stpl.tech.scm.data.model.DayWiseSlotWiseSalesData;
import com.stpl.tech.scm.data.model.HolidaysListData;
import com.stpl.tech.scm.data.model.UnitWiseOrderingStrategyData;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.List;
import java.util.Date;

@Repository
public class DemandForecastingDaoImpl extends SCMAbstractDaoImpl implements DemandForecastingDao {

    @Override
    public UnitWiseOrderingStrategyData getUnitWiseOrderingStrategyDataByUnitId(Integer unitId) {
        String hql = "FROM UnitWiseOrderingStrategyData u WHERE u.unitId = :unitId";
        Query query = manager.createQuery(hql);
        query.setParameter("unitId", unitId);
        
        try {
            return (UnitWiseOrderingStrategyData) query.getSingleResult();
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public List<DayWiseSlotWiseSalesData> getHistoricalSalesData(Integer unitId, Integer brandId, Date startDate, Date endDate) {
        String hql = "FROM DayWiseSlotWiseSalesData d WHERE d.unitId = :unitId AND d.brandId = :brandId " +
                    "AND d.businessDate >= :startDate AND d.businessDate <= :endDate " +
                    "ORDER BY d.businessDate DESC";
        Query query = manager.createQuery(hql);
        query.setParameter("unitId", unitId);
        query.setParameter("brandId", brandId);
        query.setParameter("startDate", startDate);
        query.setParameter("endDate", endDate);
        
        @SuppressWarnings("unchecked")
        List<DayWiseSlotWiseSalesData> result = query.getResultList();
        return result;
    }

    @Override
    public List<HolidaysListData> getHolidayListOfType(Date startDate, Date endDate, List<String> holidayTypes) {
        String hql = "FROM HolidaysListData h WHERE h.holidayDate >= :startDate AND h.holidayDate <= :endDate " +
                    "AND h.status = 'ACTIVE' AND h.holidayType IN :holidayTypes ORDER BY h.holidayDate ASC";
        Query query = manager.createQuery(hql);
        query.setParameter("startDate", startDate);
        query.setParameter("endDate", endDate);
        query.setParameter("holidayTypes", holidayTypes);
        
        @SuppressWarnings("unchecked")
        List<HolidaysListData> result = query.getResultList();
        return result;
    }

}
