package com.stpl.tech.scm.data.redis.service.impl;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.cache.dao.CacheManagementDao;
import com.stpl.tech.scm.core.cache.service.SkuCacheManager;
import com.stpl.tech.scm.core.service.impl.SCMMetadataServiceImpl;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.converter.EntityToDtoConverter;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.model.DerivedMappingData;
import com.stpl.tech.scm.data.model.ProductDefinitionData;
import com.stpl.tech.scm.data.model.SkuDefinitionData;
import com.stpl.tech.scm.data.model.VendorDetailData;
import com.stpl.tech.scm.data.redis.CacheUtil;
import com.stpl.tech.scm.data.redis.service.CacheRefreshService;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.ProductPriceData;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.VendorDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CacheRefreshServiceImpl implements CacheRefreshService {

    @Autowired
    private SCMMetadataServiceImpl scmMetadataService;

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private CacheManagementDao cacheManagementDao;


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM")
    public Map<Integer, ProductDefinition> reloadProductCache() {
        StopWatch totalWatch = new StopWatch();
        totalWatch.start();

        List<ProductDefinitionData> allProducts = cacheManagementDao.getAllProducts();
        List<Integer> productIds = allProducts.stream()
                .map(ProductDefinitionData::getProductId)
                .toList();
        Map<Integer, List<DerivedMappingData>> derivedMappingsMap = cacheManagementDao.getDerivedMappingsForProducts(productIds);
        Map<Integer, ProductDefinition> productCache = refreshProductDefinitionCache(allProducts, derivedMappingsMap);

        totalWatch.stop();
        log.info("✅ Product cache reload completed. Total products: {}. End-to-end time: {} ms", productCache.size(), totalWatch.getTotalTimeMillis());

        return productCache;
    }

    private Map<Integer, ProductDefinition> refreshProductDefinitionCache(
            List<ProductDefinitionData> allProducts,
            Map<Integer, List<DerivedMappingData>> derivedMappingsMap) {

        Map<Integer, String> employees = masterDataCache.getEmployees();
        Map<Integer, ProductPriceData> productPrices = scmCache.getProductPrices();
        Map<Integer, ProductDefinition> productDefinitions = new HashMap<>(allProducts.size());

        for (ProductDefinitionData data : allProducts) {
            data.setDerivedMappingDataList(derivedMappingsMap.getOrDefault(data.getProductId(), new ArrayList<>()));
            IdCodeName createdBy = SCMUtil.generateIdCodeName(data.getCreatedBy(), "", employees.get(data.getCreatedBy()));
            ProductDefinition pd = SCMDataConverter.convert(data, createdBy);
            productDefinitions.put(pd.getProductId(), pd);
            productPrices.put(pd.getProductId(), SCMDataConverter.convertToPrice(pd));
        }
        return productDefinitions;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM")
    public Map<Integer, SkuDefinition> reloadSkuCache() {
        StopWatch totalWatch = new StopWatch();
        totalWatch.start();

        List<SkuCacheManager.SkuWithProduct> skuList = cacheManagementDao.getAllSkus();
        Map<Integer, SkuDefinition> skuCache =  refreshSkuDefinitionCache(skuList);

        totalWatch.stop();
        log.info("✅ Sku cache reload completed. Total skus: {}. End-to-end time: {} ms", skuCache.size(), totalWatch.getTotalTimeMillis());
        return skuCache;
    }

    private Map<Integer, SkuDefinition> refreshSkuDefinitionCache(List<SkuCacheManager.SkuWithProduct> skuList) {
        Map<Integer, String> employees = masterDataCache.getEmployees();
        Map<Integer, SkuDefinition> skuDefinitions = new HashMap<>();
        skuList.forEach(swp -> {
            SkuDefinitionData data = swp.getSkuData();
            IdCodeName createdBy = SCMUtil.generateIdCodeName(data.getCreatedBy(), "", employees.get(data.getCreatedBy()));
            skuDefinitions.put(data.getSkuId(), EntityToDtoConverter.cacheConverter(data, createdBy, swp.getProduct()) );
        });
        return skuDefinitions;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM")
    public Map<Integer, VendorDetail> reloadVendorCache() {
        StopWatch totalWatch = new StopWatch();
        totalWatch.start();
        List<VendorDetailData> allVendors = scmMetadataService.getAllVendors();
        Map<Integer, VendorDetail> vendorCache = refreshVendorDetailCache(allVendors);
        totalWatch.stop();
        log.info("✅ Vendor cache reload completed. Total vendors: {}. End-to-end time: {} ms", vendorCache.size(), totalWatch.getTotalTimeMillis());
        return vendorCache;
    }

    private Map<Integer, VendorDetail> refreshVendorDetailCache(List<VendorDetailData> vendorList) {
        if (CollectionUtils.isEmpty(vendorList)) {
            return new HashMap<>();
        }
        Map<Integer, VendorDetail> vendorDetails = new HashMap<>();
        Map<Integer, String> employees = masterDataCache.getEmployees();
        vendorList.forEach(data -> {
            try {
                String updatedBy = employees.get(data.getUpdatedBy());
                String requestedBy = employees.get(data.getRequestedBy());
                VendorDetail detail = SCMDataConverter.convertVendor(data, updatedBy, requestedBy);
                if (Objects.nonNull(data.getLastUnBlockedBy())) {
                    detail.setLastUnBlockedBy(SCMUtil.getCreatedBy(employees.get(data.getLastUnBlockedBy()), data.getLastUnBlockedBy()));
                }
                if (Objects.nonNull(data.getLastBlockedBy())) {
                    detail.setLastBlockedBy(SCMUtil.getCreatedBy(employees.get(data.getLastBlockedBy()), data.getLastBlockedBy()));
                }
                vendorDetails.put(detail.getVendorId(), detail);
            } catch (Exception e) {
                log.error("Error in converting vendor cache for ID ; {}.  {}", data.getVendorId(), e.getMessage());
            }
        });
        return vendorDetails;
    }

}
