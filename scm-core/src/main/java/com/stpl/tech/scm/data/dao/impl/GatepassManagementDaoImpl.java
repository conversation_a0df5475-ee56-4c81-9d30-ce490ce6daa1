package com.stpl.tech.scm.data.dao.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.Query;

import com.stpl.tech.scm.core.util.ValidationUtil;
import com.stpl.tech.util.domain.RequestContext;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.dao.GatepassManagementDao;
import com.stpl.tech.scm.data.model.GatepassData;
import com.stpl.tech.scm.data.model.GatepassVendorMappingData;
import com.stpl.tech.scm.domain.model.SearchGatepass;

@Repository
public class GatepassManagementDaoImpl extends SCMAbstractDaoImpl implements GatepassManagementDao {


	@Override
	public List<GatepassVendorMappingData> getVendorMappingList(String operationType, Integer unitId, Integer vendorId, String status) {
		StringBuilder query = new StringBuilder("FROM GatepassVendorMappingData g WHERE 1=1 ");
		if (status != null) {
			query.append(" and g.status = :status ");
		}
		if (operationType != null) {
			query.append(" and g.operationType = :operationType ");
		}
		if (unitId != null) {
			query.append(" and g.unitId = :unitId ");
		}
		if(vendorId != null) {
			query.append(" and g.vendorId = :vendorId ");
		}
		Query queryString = manager.createQuery(query.toString());
		if (operationType != null) {
			queryString.setParameter("operationType", operationType);
		}
		if (unitId != null) {
			queryString.setParameter("unitId", unitId);
		}
		if (status != null) {
			queryString.setParameter("status", status);
		}
		if(vendorId != null) {
			queryString.setParameter("vendorId", vendorId);
		}
		return queryString.getResultList();
	}

    @Override
    public List<GatepassData> getGatepass(SearchGatepass gatePassRequest) {
        StringBuilder queryString = new StringBuilder("FROM GatepassData g WHERE 1=1 ");
        Map<String, Object> params = new HashMap<>();

        if (gatePassRequest.getUnitId() != null) {
            queryString.append(" AND g.sendingUnit = :sendingUnit ");
            params.put("sendingUnit", gatePassRequest.getUnitId());
        }
        if (gatePassRequest.getOperationType() != null) {
            queryString.append(" AND g.operationType = :operationType ");
            params.put("operationType", gatePassRequest.getOperationType());
        }
        if (!ValidationUtil.checkIsEmptyCollection(gatePassRequest.getStatuses())) {
            queryString.append(" AND g.status IN :statuses ");
            params.put("statuses", gatePassRequest.getStatuses());
        }
        if (gatePassRequest.getVendorSelected() != null) {
            queryString.append(" AND g.vendorId = :vendorSelected ");
            params.put("vendorSelected", gatePassRequest.getVendorSelected());
        }
        if (ValidationUtil.checkStringIsNotEmpty(gatePassRequest.getStartDate())) {
            queryString.append(" AND g.createdAt >= :startDate ");
            params.put("startDate", SCMUtil.parseDate(gatePassRequest.getStartDate()));
        }
        if (ValidationUtil.checkStringIsNotEmpty(gatePassRequest.getEndDate())) {
            queryString.append(" AND g.createdAt <= :endDate ");
            params.put("endDate", SCMUtil.parseDate(gatePassRequest.getEndDate()));
        }
        if (gatePassRequest.getGatePassId() != null) {
            queryString.append(" AND g.id = :gatePassId ");
            params.put("gatePassId", gatePassRequest.getGatePassId());
        }
        if(gatePassRequest.isForApproval()) {
            queryString.append(" AND g.approvalRequestedTo = :approvalRequestedToId");
            params.put("approvalRequestedToId", RequestContext.getContext().getLoggedInUserId());
        }
        queryString.append(" ORDER BY g.createdAt DESC");
        Query query = manager.createQuery(queryString.toString());

        params.forEach(query::setParameter);
        if (params.isEmpty()) {
            query.setMaxResults(30);
        }

        return query.getResultList();
    }


}
