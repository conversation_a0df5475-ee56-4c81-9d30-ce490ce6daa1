package com.stpl.tech.scm.service.controller;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.domain.model.ApiResponse;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.GatepassException;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.GatepassManagementService;
import com.stpl.tech.scm.domain.model.Gatepass;
import com.stpl.tech.scm.domain.model.GatepassApprovalRequest;
import com.stpl.tech.scm.domain.model.GatepassOperationType;
import com.stpl.tech.scm.domain.model.GatepassVendorMapping;
import com.stpl.tech.scm.domain.model.ProductStatus;
import com.stpl.tech.scm.domain.model.SearchGatepass;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;
import java.text.ParseException;
import java.util.List;

@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
		+ SCMServiceConstants.GATEPASS_MANAGEMENT_ROOT_CONTEXT)
public class GatepassManagementResource extends AbstractSCMResources {
	@Autowired
	private GatepassManagementService gatepassManagementService;

	@RequestMapping(method = RequestMethod.GET, value = "vendor-list", produces = MediaType.APPLICATION_JSON)
	public List<GatepassVendorMapping> getVendorOpertaionMapping(@RequestParam(required = false) final String opsType,
			@RequestParam(required = false) final Integer unitId, @RequestParam(required = false) final String status) {
		return gatepassManagementService.getVendorMappingList(opsType, unitId, status);
	}

	@RequestMapping(method = RequestMethod.POST, value = "create-gatepass", produces = MediaType.APPLICATION_JSON)
	public Integer createGatepass(@RequestBody Gatepass gatepass) throws InventoryUpdateException, GatepassException, SumoException, ParseException, DataNotFoundException {
		return gatepassManagementService.createGatepass(gatepass);
	}

	@RequestMapping(method = RequestMethod.POST, value = "search-gatepass", produces = MediaType.APPLICATION_JSON)
	public List<Gatepass> getGatepassList(@RequestBody SearchGatepass searchGatepass) {
		return gatepassManagementService.getGatepass(searchGatepass);
	}

	@RequestMapping(method = RequestMethod.POST, value = "gatepass-details", produces = MediaType.APPLICATION_JSON)
	public List<Gatepass> getGatepassDetails(@RequestBody SearchGatepass searchGatepass) {
		return gatepassManagementService.getGatepassDetails(searchGatepass);
	}

	@RequestMapping(method = RequestMethod.POST, value = "update-gatepass", produces = MediaType.APPLICATION_JSON)
	public Boolean updateGatepass(@RequestBody Gatepass gatepass) throws InventoryUpdateException, GatepassException, SumoException, ParseException, DataNotFoundException {
		return gatepassManagementService.updateGatepass(gatepass);
	}

	@RequestMapping(method = RequestMethod.POST, value = "cancel-gatepass", produces = MediaType.APPLICATION_JSON)
	public Boolean cancelGatepass(@RequestBody Gatepass gatepass) throws InventoryUpdateException, SumoException {
		return gatepassManagementService.cancelGatepass(gatepass);
	}
	

	@RequestMapping(method = RequestMethod.POST, value = "add-vendor-mapping", produces = MediaType.APPLICATION_JSON)
	public Boolean addVendorMapping(@RequestBody GatepassVendorMapping vendorMapping) throws GatepassException, SumoException{
		return gatepassManagementService.addVendorMapping(vendorMapping);
	}
	
	@RequestMapping(method = RequestMethod.PUT, value = "vendor-deactivate", produces = MediaType.APPLICATION_JSON)
	public Boolean deactivateVendor(@RequestBody final int mappingId) {
		return gatepassManagementService.updateVendorStatus(mappingId, ProductStatus.IN_ACTIVE.name());
	}

	@RequestMapping(method = RequestMethod.PUT, value = "vendor-activate", produces = MediaType.APPLICATION_JSON)
	public Boolean activateVendor(@RequestBody final int mappingId) {
		return gatepassManagementService.updateVendorStatus(mappingId, ProductStatus.ACTIVE.name());
	}

    @PutMapping(value = "update-mapping", produces = MediaType.APPLICATION_JSON)
    public ApiResponse updateMapping(@RequestBody GatepassVendorMapping gatepassVendorMapping) {
        return gatepassManagementService.updateMapping(gatepassVendorMapping);
    }

	@PutMapping(value = "approve-gatepass", produces = MediaType.APPLICATION_JSON)
	public Boolean approveGatepass(@RequestBody GatepassApprovalRequest approvalRequest) throws GatepassException, SumoException, DataNotFoundException, InventoryUpdateException {
		return gatepassManagementService.approveGatepass(approvalRequest);
	}

	@PutMapping(value = "reject-gatepass", produces = MediaType.APPLICATION_JSON)
	public Boolean rejectGatepass(@RequestBody GatepassApprovalRequest approvalRequest) throws GatepassException, SumoException, DataNotFoundException, InventoryUpdateException {
		return gatepassManagementService.rejectGatepass(approvalRequest);
	}

}
