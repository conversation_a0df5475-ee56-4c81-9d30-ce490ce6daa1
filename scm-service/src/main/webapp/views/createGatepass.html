<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
	<div class="col s12">
		<div class="row">
			<div class="col s6 m6 l6">
				<h4>Gatepass</h4>
			</div>
			<dev class="col s6">
				<div class="form-element" style="padding-top: 24px;margin-bottom: 0px;">
					<input
							id="isForFixedAsset"
							data-ng-model="gatepass.assetGatePass"
							data-ng-change="toggleFixedAssetFlag()"
							type="checkbox" />
					<label
							class="black-text"
							for="isForFixedAsset">For Fixed Asset</label>
				</div>
			</dev>
		</div>
	</div>
	<form name="gatepassForm" novalidate>
		<div class="row">
			<div class="col s12 m6 l6">
				<label>Select Operation Type:</label> <select
					data-ng-model="gatepass.operationType"
					data-ng-change="getOperationVendorList(gatepass.operationType)"
					data-ng-options="ops.name as ops.label  for ops in operationList"
					required="required">
				</select>
			</div>
			<div class="col s6 m3 l3">
				<label>Select Vendor :</label>
                <select
                        data-ng-model="selectedDetail"
                        data-ng-options="detail as detail.vendor.entityName for detail in vendorList"
                        data-ng-change="selectVendor(selectedDetail)"
                        required="required">
                </select>
			</div>
			<div class="col s6 m3 l3" data-ng-if="locationList.length>0">
				<label>Select Dispatch Location :</label> <select id="locationList"
					name="locationList" data-ng-model="selectedLocation"
					data-ng-options="location as location.city for location in locationList track by location.dispatchId"
					data-ng-change="selectDispatchLocation(selectedLocation)"
					required="required"></select>
			</div>
		</div>
		<div class="row">
			<!-- <div class="col s12 m6 l6">
				<label>Select Reason :</label> <select
					data-ng-model="gatepass.reason"
					data-ng-options="reason.name as reason.label  for reason in reasonList"
					required="required">
				</select>
			</div> -->
			<div class="col s12 m6 l6">
				<label>Enter Additional Charges(If any) :</label>
				<div class="form-element">
					<input type="number" name="additionalCharges"
						data-ng-model="gatepass.additionalCharges" />
				</div>
			</div>
			<div class="col s6 m3 l3" style="margin-top: 30px;">
				<input id="isReturnable" data-ng-model="gatepass.returnable"
					type="checkbox"
					data-ng-change="updateReturnDays(gatepass.returnable, gatepass.expectedReturn)" />
				<label class="black-text" for="isReturnable">Returnable</label>
			</div>

			<div class="col s6 m3 l3" data-ng-if="gatepass.returnable == true">
				<label>No Of days:</label> <select
					data-ng-model="gatepass.expectedReturn"
					data-ng-options="day as day  for day in daysList"
					required="required">
				</select>
			</div>
		</div>
		<div class="row">
			<div class="col s12 m6 l6">
				<div class="form-element">
					<label for="dateIssued">Issue date</label> <input input-date
						type="text" name="created" id="dateIssued"
						ng-model="gatepass.issueDate" container="" format="yyyy-mm-dd"
						select-years="1" min="{{minIssueDate}}" max="{{maxIssueDate}}" />
				</div>
			</div>
			<div class="col s12 m6 l6">
				<label>Select Product:</label> <select ui-select2
					ng-model="selectedProduct" data-placeholder="Enter name of product">
					<option value=""></option>
					<option ng-repeat="product in scmProductDetails | filter:fixedAssetFilter track by product.productId "
						value="{{product}}">{{product.productName}}</option>
				</select> <input type="button" class="btn" value="ADD PRODUCT"
					data-ng-click="addNewTOItem()" />
			</div>
		</div>
		<!-- <div class="row">
			<div class="col s6 m3 l3">
				<label>Enter Additional Charges(If any) :</label>
			</div>
			<div class="col s6 m3 l3">
				<div class="form-element">
					<input type="number" name="additionalCharges"
						data-ng-model="gatepass.additionalCharges" />
				</div>
			</div>
			<div class="col s12 m6 l6">
				<div class="form-element">
					<input type="button" class="btn" value="ADD PRODUCT"
						data-ng-click="addNewTOItem()" />
				</div>
			</div>
		</div> -->
		<div class="row" data-ng-show="gatepass.assetGatePass == true">
			<dev draggable1 class="col s3">
				<label style="color: whitesmoke;">Focus to scan   </label>
				<!--ng-keypress="keyPressHandler($event)"-->
				<input id="_draggableInputId" type="text" max-length="6" name="scan" data-ng-model="scannedAssetTagValue"  data-ng-change="onScan()" stopEvent>
			</dev>
		</div>
		<div class="row" data-ng-show="gatepassProducts.length>0">

			<ul class="collection z-depth-1-half" style="margin-bottom: 50px;">
				<li class="collection-item list-head">
					<div class="row" style="margin-bottom: 0;">
						<div class="col s6">Product Name</div>
						<div class="col s3">Transferred Quantity</div>
						<div class="col s3">Unit Of Measure</div>
					</div>
				</li>
				<li style="margin-bottom: 10px; border: #ddd 1px solid;" data-ng-repeat="item in gatepassProducts track by item.productId">
					<div class="row"
						style="padding: 10px; background: #eee; border-bottom: #ddd 1px solid;">
						<div class="col s6">
							<a data-ng-click="showPreview($event, item.productId,'PRODUCT')">{{item.productName}}</a>
						</div>
						<div class="col s3">{{item.transferredQuantity == null ? 0 :
							item.transferredQuantity}}</div>
						<div class="col s3">{{item.unitOfMeasure}}</div>
					</div>
					<div class="row">
						<div class="col s5">
							<label>Select SKU</label> <select
								data-ng-model="item.selectedSku"
								data-ng-options="sku as sku.skuName for sku in item.skuList track by sku.skuId"></select>
						</div>
						<div class="col s5">
							<label>Select Packaging:</label> <select
								data-ng-model="item.selectedPackaging"
								data-ng-options="packaging as packaging.packagingDefinition.packagingName group by packaging.packagingDefinition.packagingType for packaging
                                     in item.selectedSku.skuPackagings | orderBy: 'packagingDefinition.packagingType' track by packaging.packagingId"
								data-ng-init="item.selectedPackaging=item.selectedSku.skuPackagings[0]"></select>
						</div>
						<div class="col s2">
							<input type="button" value="Add" class="btn"
								data-ng-click="addPackaging(item)" style="margin-top: 20px;" />
							<input type="button" value="Remove" class="btn red"
								data-ng-click="gatepassProducts.splice($index, 1)"
								style="margin-top: 20px;" />
						</div>
					</div>
					<div class="row" data-ng-repeat="trItem in item.trPackaging track by $index">
						<div class="col s12">
							<div class="row">
								<div class="col s6">
									<a data-ng-click="showPreview($event, trItem.skuId,'SKU')">{{trItem.skuName}}</a>
								</div>
								<div class="col s6">Transferred Qty:
									{{trItem.transferredQuantity}} &nbsp; {{trItem.unitOfMeasure}}</div>
							</div>
							<div class="row"
								data-ng-repeat="pgd in trItem.packagingDetails track by $index">
								<div>
									<div class="col s2">{{pgd.packagingDefinitionData.packagingName}}</div>
									<div class="col s2">
										<label>Units Packed:</label> <input type="number"
											name="pkgQty[$index]" data-ng-model="pgd.numberOfUnitsPacked"
											ng-change="updatePackagingQty(pgd,trItem,item)" required />
										<p ng-show="trForm.pkgQty[$index].$error.required"
											class="errorMessage">Please enter valid quantity.</p>
									</div>
									<div class="col s2">
										<label>Transferred Qty:</label> {{pgd.transferredQuantity}}
									</div>
									<div class="col s2">
										<label>Unit Of Measure:</label>
										{{pgd.packagingDefinitionData.unitOfMeasure}}
									</div>
									<div class="col s2">
										<button class="btn btn-small" type="button"
											data-ng-click="removePackaging(trItem,$index, item)">Remove</button>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col s4" data-ng-repeat="asset in trItem.gatepassItemAssetMappings">
									<label>Enter 6 digit Asset Tag Value:</label>
									<input type="text"

										   name="assetTag"
										   data-ng-model="asset.assetTagValue"
										   data-ng-minlength="0"
										   data-ng-maxlength="6"
										   ng-change="validateAssetTagValue(asset.assetTagValue,asset,trItem, $index, item)" stopEvent required/>
									<p ng-show="asset.assetTagValue == null" class="errorMessage">Asset Tag Value is required.</p>
									<p ng-show="asset.assetTagValue !=  null && asset.assetTagValue.length > 6" class="errorMessage">Asset Tag Value is too large.</p>
									<p ng-show="asset.assetTagValue != null && asset.assetTagValue.length != 6" class="errorMessage">Asset Tag Value is too small.</p>
									<div data-ng-if="asset.assetTagValue == null || !asset.assetValidated">
										<p  class="errorMessage">Enter Valid Asset Tag Value.</p>
									</div>
								</div>
							</div>
						</div>
					</div>
				</li>
			</ul>
			<div class="row">
				<div class="col s12 form-element">
					<label>Comment:</label>
					<textarea data-ng-model="gatepass.comment" ></textarea>
				</div>
                <div class="form-group">
                    <label class="form-label"><i class="material-icons tiny">verified_user</i> Request Price approval from*</label>
                    <select id="inputCreated2" ui-select2="selectEmployee" class="form-control"
                            data-ng-model="approver"
                            data-ng-change="setApprover(approver)"
                            data-placeholder="Select Approver"
                            data-ng-options="s as (s.name + ' [' + s.id + ']') for s in approversMap | orderBy : 'name' track by s.id">
                    </select>
                </div>
				<div class="col s12 form-element">
					<input type="button" data-ng-if="gatepassForm.$valid"
						data-ng-click="createGatepass()" class="btn right" value="Submit" />
				</div>
			</div>

		</div>
	</form>
</div>
