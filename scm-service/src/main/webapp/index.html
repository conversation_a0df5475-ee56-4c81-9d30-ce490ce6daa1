<!doctype html>
<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CHAAYOS - SUMO</title>
    <link rel="icon" type="image/png" sizes="32x32" href="favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="96x96" href="favicon-96x96.png">
    <link rel="icon" type="image/png" sizes="16x16" href="favicon-16x16.png">

    <link rel="stylesheet" href="https://d33m6da7d295ei.cloudfront.net/7.3.81/css/static.1e3b6a47.css" />

    <link rel="stylesheet" href="https://dvkqc6sjvl6ta.cloudfront.net/7.3.81/css/content.f126f003.css" />

    <script type="text/javascript" src="https://d33m6da7d295ei.cloudfront.net/7.3.81/js/static.d958ae17.js"></script>
    <script type="text/javascript" src="https://dvkqc6sjvl6ta.cloudfront.net/7.3.81/js/content.3c0f25b6.js"></script>
    <script type="text/javascript" src="https://dvkqc6sjvl6ta.cloudfront.net/7.3.81/js/partials.0c018acc.js"></script>

    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons" >
    <!-- custom method to disable back button -->
    <script type = "text/javascript" >
		window.version = "7.3.81/";
		window.analyticsUrl = "https://internal.chaayos.com/";
		window.scmUrl = "https://internal.chaayos.com/";
		window.masterUrl = "https://internal.chaayos.com/";
		window.crmUrl = "https://internal.chaayos.com/";
		window.kettleUrl = "https://internal.chaayos.com/";
		window.imageUrl = "https://d8xnaajozedwc.cloudfront.net/";
        history.pushState(null, null, '');
        window.addEventListener('popstate', function(event) {
            history.pushState(null, null, '');
        });
    </script>
    <!-- custom method to disable back button -->

</head>
<body data-ng-app="scmApp">
    <!-- Spinner code starts here-->
    <div class="overlay" data-ng-show="showSpinner">
        <div id="scm-spinner" class="preloader-wrapper big active">
            <div class="spinner-layer spinner-blue-only">
                <div class="circle-clipper left">
                    <div class="circle"></div>
                </div>
                <div class="gap-patch">
                    <div class="circle"></div>
                </div>
                <div class="circle-clipper right">
                    <div class="circle"></div>
                </div>
            </div>
        </div>
    </div>
    <!-- Spinner code ends here-->
    <div class="overlay" data-ng-show="showOverlay"></div>


    <div class="row" ui-view></div>


    <!-- Alert Service Template  -->
    <div id="materialModal" class="modal hide">
        <div class="modal-overlay"></div>
        <div id="materialModalCentered">
            <div id="materialModalContent" onclick="event.stopPropagation()">
                <div id="materialModalTitle">&nbsp;</div>
                <div id="materialModalText">&nbsp;</div>
                <div id="materialModalButtons">
                    <div id="materialModalButtonDismiss" class="materialModalButton" onclick="closeMaterialAlert(event, true)">
                        <a>OK</a>
                    </div>
                    <div id="materialModalButtonOK" class="materialModalButton" onclick="closeMaterialAlert(event, true)">
                        <a>Yes</a>
                    </div>
                    <div id="materialModalButtonCANCEL" class="materialModalButton"
                         onclick="closeMaterialAlert(event, false)">
                        <a>NO </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--Upload file template-->
    <div id="fileUploadServiceModal" class="hide">
        <div class="modal-overlay"></div>
        <div id="fileUploadModalCentered">
            <div id="fileUploadModalContent" class="row" onclick="event.stopPropagation();">
                <div class="col s12">
                    <div id="fileModalTitle" style="font-size: 18px;">File Upload</div>
                    <div class="file-field input-field">
                        <div class="btn btn-small">
                            <input id="fileToUpload" class="pull-left" type="file"> <span id="fileModalText">Choose File</span>
                        </div>
                        <div class="file-path-wrapper">
                            <input id="uploadFilePath" class="file-path validate" type="text" placeholder="Select File">
                        </div>
                        <span id="fileUploadError"></span>
                    </div>
                    <div id="fileUploadButtons">
                        <button class="waves-effect waves-green btn"
                                onclick="uploadFile(event,true)">UPLOAD
                        </button>
                        <button class="red waves-effect waves-red btn"
                                onclick="closeFileModal(event)">CANCEL
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <script type="text/ng-template" id="metadataLoader.html">
        <div data-ng-init="initMetaData()" class="row"
             style="margin: 10rem 25rem; border: 1px solid #ddd; padding: 5rem;">
            <h5 class="center">Loading Metadata Before Logging In...</h5>
            <div class="progress" style="margin: 1.5rem 0 1rem 0 !important;">
                <div class="indeterminate"></div>
            </div>
        </div>
    </script>
</body>
</html>