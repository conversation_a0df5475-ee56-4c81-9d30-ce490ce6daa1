'use strict';
angular.module('scmApp')
    .controller('dayCloseCtrl', ['$rootScope', '$scope', 'authService', 'appUtil', 'apiJson',
        '$toastService', '$http', '$alertService', 'pagerService', 'packagingService', 'previewModalService', 'Popeye', '$timeout','uiGridConstants','$fileUploadService',
        function ($rootScope, $scope, authService, appUtil, apiJson, $toastService, $http, $alertService, pagerService, packagingService, previewModalService, Popeye, $timeout, uiGridConstants,$fileUploadService) {


            $scope.startAutoDayClose = function (unitId, userId) {
                $http({
                    method: "POST",
                    url: apiJson.urls.warehouseClosing.kWhAutoDayClose,
                    params: {
                        "unitId" : unitId,
                        "userId" : userId
                    }
                }).then(function (response) {
                    $toastService.create("Please Check Your Email Regarding the status of Day Close");
                }, function (error) {
                    if (error.data.errorMessage != undefined && error.data.errorMessage != null) {
                        $alertService.alert("Error in Auto Day Close Process!", error.data.errorMessage, function () {
                            console.log("Got error while doing day close");
                        }, true);
                    } else {
                        $toastService.create("Something went Wrong ..!");
                    }
                });
            };

            function executeStep(logs) {
                logs = logs.filter(function (log) {
                    return log.status == "ACTIVE";
                });
                if ($scope.isAllListType && !$scope.isAccessToAuditDayClose) {
                    return;
                }
                var type = appUtil.isEmptyObject(logs) ? "STARTED" : logs[0].type;
                switch (type) {
                        case "RECEIVINGS":
                            $scope.ackBookings();
                            break;
                        case "BOOKINGS":
                            $scope.ackReverseBookings();
                            break;
                        case "REVERSE_BOOKING":
                            $scope.ackTransfers();
                            break;
                        case "TRANSFERS":
                            $scope.ackGatepasses();
                            break;
                        case "GATEPASS":
                            $scope.ackInvoices();
                            break;
                        case "INVOICE":
                            $scope.ackReturns();
                            break;
                        case "GATEPASS_RETURN":
                            $scope.ackWastages();
                            break;
                        case "WASTAGES":
                            $scope.ackInventory();
                            break;
                        case "INVENTORY":
                        case "CORRECTION":
                            showSummary();
                            break;
                        default :
                            $scope.ackReceivings();
                            break;
                    }
                }

                function getTxns(url, callback) {
                    $http({
                        method: "GET",
                        url: url
                    }).then(function (response) {
                        if (!appUtil.isEmptyObject(response) && !appUtil.isEmptyObject(response.data)) {
                            callback(response.data);
                        }
                    }, function (error) {
                        $alertService.alert("Error in Day Close Process!", error.data.errorMessage, function () {
                            console.log("Got error while doing day close");
                        }, true);
                    });
                }

                $scope.ackReceivings = function () {
                    $scope.wizardStep = 1;
                    getTxns(apiJson.urls.warehouseClosing.receivings + "/" + $scope.unitData.id + "/" + $scope.dayCloseEvent.id,
                    function (response) {
                        $scope.receivings = response;
                    }

                )
                    ;
                };

                $scope.ackBookings = function () {
                    $scope.wizardStep = 2;
                    getTxns(apiJson.urls.warehouseClosing.bookings + "/" + $scope.unitData.id + "/" + $scope.dayCloseEvent.id,
                        function (response) {
                            $scope.bookings = response.list;
                            $scope.eventIds=response.events;
                        }
                    );
                };

                $scope.ackReverseBookings = function () {
                    $scope.wizardStep = 3;
                    getTxns(apiJson.urls.warehouseClosing.reverseBookings + "/" + $scope.unitData.id + "/" + $scope.dayCloseEvent.id,
                        function (response) {
                            $scope.reverseBookings = response.list;
                            $scope.eventIds=response.events;
                        }
                    );
                };

                $scope.ackTransfers = function () {
                    $scope.wizardStep = 4;
                    getTxns(apiJson.urls.warehouseClosing.transfers + "/" + $scope.unitData.id + "/" + $scope.dayCloseEvent.id,
                        function (response) {
                            $scope.transfers = response;
                        }
                    );
                };

                $scope.ackGatepasses = function () {
                    $scope.wizardStep = 5;
                    getTxns(apiJson.urls.warehouseClosing.gatepasses + "/" + $scope.unitData.id + "/" + $scope.dayCloseEvent.id,
                        function (response) {
                            $scope.gatepasses = response;
                        }
                    );
                };

                $scope.ackInvoices = function () {
                    $scope.wizardStep = 6;
                    getTxns(apiJson.urls.warehouseClosing.invoices + "/" + $scope.unitData.id + "/" + $scope.dayCloseEvent.id,
                        function (response) {
                            $scope.invoices = response;
                        }
                    );
                };

                $scope.ackReturns = function () {
                    $scope.wizardStep = 7;
                    getTxns(apiJson.urls.warehouseClosing.returns + "/" + $scope.unitData.id + "/" + $scope.dayCloseEvent.id,
                        function (response) {
                            $scope.returns = response;
                        }
                    );
                };

                $scope.ackWastages = function () {
                    $scope.wizardStep = 8;
                    getTxns(apiJson.urls.warehouseClosing.wastages + "/" + $scope.unitData.id + "/" + $scope.dayCloseEvent.id ,
                        function (response) {
                            $scope.wastages = response;
                        }
                    );
                };

                $scope.listType  = function (){
                    $scope.isAllListType = !$scope.isAllListType;

                }

                $scope.ackInventory = function () {
                    $scope.wizardStep = 9;
                    $scope.loadedInventoryList = false;
                    var url = apiJson.urls.warehouseClosing.inventoryLists + "/" + $scope.unitData.id + "?isAllListType=" + $scope.isAllListType
                    $http({
                        method: "POST",
                        url: url,
                        data : $scope.isAllListType ? $scope.selectedSubCategories : []
                    }).then(function (response) {
                        if (!appUtil.isEmptyObject(response) && !appUtil.isEmptyObject(response.data)) {
                            $scope.inventoryList = response.data;
                            $scope.setMaxDateForSku($scope.inventoryList);
                        }
                        $scope.loadedInventoryList = true;
                    }, function (error) {
                        $alertService.alert("Error in Day Close Process!", error.data.errorMessage, function () {
                            console.log("Got error while doing day close");
                        }, true);
                    });
                };

            $scope.setMaxDateForSku = function (inventoryList) {
                $scope.maxDatesOfSku = {};
                angular.forEach(inventoryList, function (inventoryListItem) {
                    var product = $scope.productMap[inventoryListItem.productId];
                    var maxDate = null;
                    if (product !== undefined && product !== null) {
                        if (product.shelfLifeInDays > 0) {
                            maxDate = appUtil.formatDate(appUtil.getDate(product.shelfLifeInDays), "yyyy-MM-dd");
                        } else {
                            maxDate = appUtil.formatDate(appUtil.getDate(1), "yyyy-MM-dd");
                        }
                    } else {
                        maxDate = appUtil.formatDate(appUtil.getDate(1), "yyyy-MM-dd");
                    }
                    $scope.maxDatesOfSku[inventoryListItem.skuId] = maxDate;
                    inventoryListItem.originalExpecteedClosing = angular.copy(inventoryListItem.stockValue);
                });
            };

               $scope.uploadDoc = function () {
                  $fileUploadService.openFileModal("Upload Stock Sheet", "Find", function (file) {
                  $scope.uploadStockSheet(file);
                  });
                   };

                $scope.uploadStockSheet = function (file) {
                $rootScope.showFullScreenLoader = true;
                 var fd = new FormData(document.forms[0]);
                 fd.append("file", file);
                                $http({
                                    url: apiJson.urls.warehouseClosing.uploadStockSheet,
                                    method: 'POST',
                                    data: fd,
                                    headers: {'Content-Type': undefined},
                                    transformRequest: angular.identity
                                }).success(function (response) {
                                    $rootScope.showFullScreenLoader = false;
                                    if (!appUtil.isEmptyObject(response)) {
                                        $scope.stock = response;
                                        fillValue();

                                    } else {
                                        $scope.stock = [];
                                        $toastService.create("Could not find any valid stocks from sheet.");
                                    }
                                }).error(function (response) {
                                    $rootScope.showFullScreenLoader = false;
                                    if (response.errorMsg != null) {
                                        $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                                    } else {
                                        $toastService.create("Error in uploaded sheet.");
                                    }
                                });
                            };

                function fillValue(){
                var count = 0;
                if($scope.isAllListType){
                $scope.varianceReasonList = ["Wrong Transaction done", "Counting error", "Yield issue in recipe", "Stock lost", "GR missing", "Recipe issue","Audit Variance"];
                }
                angular.forEach($scope.stock,function(stockProduct){
                  angular.forEach($scope.inventoryList, function (product) {
                  if(stockProduct.skuId == product.skuId)
                  angular.forEach(product.packagings, function (packaging) {
                  if(packaging.packagingType === "LOOSE" && stockProduct.stock!=null && stockProduct.stock > 0 ){
                  packaging.value = stockProduct.stock;
                   }
                   if(packaging.packagingType === "LOOSE" && stockProduct.stock==null){
                   packaging.value = 0;
                   count++;
                   }
                   if(stockProduct.stock == null ){
                   stockProduct.stock=0;
                   }
                    product.variance = product.expectedValue - stockProduct.stock;
                   product.stockValue = calculateVariance(product.packagings);
                   product.varianceCost = product.variance * product.unitPrice;
                    if(product.variance!=0){
                    product.varianceReason = "Audit Variance";
                    }
                    if(product.variance < 0 && stockProduct.stock>0){
                   product.selectedExpiry =$scope.maxDatesOfSku[product.skuId];
                   product.expiryTime = "13";
                   $scope.setExpiryDate(product.selectedExpiry,product.expiryTime,product);
                    }
                   });
                   });
                   });
                   if(count > 0){
                   $toastService.create( count +" stocks are null and yet will be marked 0");
                   }
                  };


                $scope.fillZero = function(){
                        angular.forEach($scope.inventoryList, function (product) {
                            angular.forEach(product.packagings, function (packaging) {
                                packaging.value = 0;
                            });
                        });
                    };

                    $scope.autoFill = function(){
                        angular.forEach($scope.inventoryList, function (product) {
                            angular.forEach(product.packagings, function (packaging) {
                               if(packaging.packagingType === "LOOSE" ){
                                packaging.value = product.stockValue;
                               } 
                            });
                        });
                    };

                $scope.changeStock = function (item) {
                    item.qty = calculateVariance(item.packagings);
                };

                $scope.changeStockValue = function (item) {
                    item.stockValue = calculateVariance(item.packagings);
                    item.variance = item.expectedValue - item.stockValue;
                    item.varianceCost = item.variance * item.unitPrice;
                };

                $scope.changeStockValueReset = function (item) {
                    var actual = item.stockValue;
                    item.stockValue = calculateVariance(item.packagings);
                    item.variance = item.expectedValue - item.stockValue;
                    item.varianceCost = (item.variance * item.unitPrice).toFixed(2);
                };


                $scope.removePackaging = function (item, index) {
                    item.packagings[index].value = undefined;
                    $scope.changeStockValueReset(item);
                };

                function calculateVariance(pkgs) {
                    var qty = 0;
                    for (var i in pkgs) {
                        var packaging = pkgs[i];
                        if (!appUtil.isEmptyObject(packaging.value)) {
                            qty = parseFloat(qty) + parseFloat(packaging.value * packaging.conversionRatio);
                            qty = parseFloat(qty).toFixed(6);
                        }
                    }
                    return qty;
                }

                function showSummary() {
                    $http({
                        method: "GET",
                        url: apiJson.urls.warehouseClosing.correctInventory + "/" + $scope.dayCloseEvent.id
                    }).then(function (response) {
                        if (!appUtil.isEmptyObject(response) && !appUtil.isEmptyObject(response.data)) {
                            $scope.ackInventorySummary(response.data);
                        } else {
                            $scope.ackInventorySummary();
                        }
                    }, function (error) {
                        console.log("Got error while getting day close");
                    });
                }

                function getInitiatedDayCloseEvent(unitId) {
                    $scope.showInitiate = true;
                    $http({
                        method: "GET",
                        url: apiJson.urls.warehouseClosing.dayClose + "/" + unitId,
                        data : $scope.getSelectedSubCategories()
                    }).then(function (response) {
                        if (!appUtil.isEmptyObject(response) && !appUtil.isEmptyObject(response.data) && response.data.status == "INITIATED") {
                            $scope.dayCloseEvent = response.data;
                            $scope.dayCloseInitiated = true;
                            $rootScope.restrictAll = true;
                            if($scope.dayCloseEvent.stockTakeType === "FULL_AUDIT_DAY_CLOSE"){
                                var aclData = $rootScope.aclData;
                                $scope.isAccessToAuditDayClose = false;
                                $scope.selectedCategoryForDayClose = $scope.dayCloseEvent.subCategories;
                                if(aclData!=null){
                                    if(aclData.action!=null && aclData.action["auddc"]!=null){
                                        $scope.isAccessToAuditDayClose = true;
                                    }else{
                                        $scope.isAccessToAuditDayClose = false;
                                    }
                                }
                                //$scope.selectedCategories.selectedValues = $scope.dayCloseEvent.categories.split(',');
                                $scope.selectedSubCategories = $scope.dayCloseEvent.subCategories.split(',');

                                $scope.isAllListType = true;
                            }

                            executeStep($scope.dayCloseEvent.eventLogs)
                        } else {
                            $scope.dayCloseInitiated = false;
                            $rootScope.restrictAll = false;
                        }
                    }, function (error) {
                        console.log("Got error while getting day close");
                    });

                }

                $scope.getSubCategories = function (categoryId){
                    var result = $scope.subCategoriesMap.get(parseInt(categoryId));
                    if(result[0].subCategoryName != "ALL"){
                        result.unshift({
                            subCategoryId: -1,
                            subCategoryName: "ALL"
                        });
                    }

                    return result;
                }

                $scope.onCategoryChange = function (value){
                    var selectedCatSize = value.length;
                    var val = value[selectedCatSize-1];
                    $scope.selectedCategory = val;

                    if($scope.selectedCategories.selectedValues.indexOf('5') != -1){
                        $scope.selectedCategories.selectedValues = ['1','2','3','4'];
                        /*for(var i = 1;i<=4;i++){
                            $scope.categories[i-1].selectedSubCategories = $scope.idToString($scope.subCategoriesMap.get(parseInt(i)));
                        }*/

                    }

                    for(var i = 1;i<=4;i++){
                        if($scope.selectedCategories.selectedValues.indexOf(String(i)) == -1){
                            $scope.categories[i-1].selectedSubCategories = [];
                        }
                    }
                }

                $scope.init = function () {
                    $scope.currentDate = appUtil.formatDate(appUtil.getDate(new Date().getHours() < 5 ? 0 : 1), "yyyy-MM-dd");
                    $scope.setProductMap();
                    $scope.receivings = [];
                    $scope.bookings = [];
                    $scope.reverseBookings = [];
                    $scope.eventIds= [];
                    $scope.transfers = [];
                    $scope.gatepasses = [];
                    $scope.invoices = [];
                    $scope.returns = [];
                    $scope.wastages = [];
                    $scope.inventoryList = [];
                    $scope.correctInventory = [];
                    $scope.blockOnPendingGrs = true ;
                    $scope.showInitiate = false;
                    $scope.dayCloseInitiated = false;
                    $scope.unitData = appUtil.getUnitData();
                    $scope.currentUser = appUtil.getCurrentUser();
                    $scope.isAccessToAuditDayClose = null;
                    $scope.varianceReasonList = ["Wrong Transaction done", "Counting error", "Yield issue in recipe", "Stock lost", "GR missing", "Recipe issue"];
                    $scope.subCategoriesMap = getCategoryToSubCategoryMap(appUtil.getMetadata().categoryDefinitions);
                    $scope.categories = [
                        {name: "COGS", id: 1, selected: false, selectedSubCategories: []},
                        {name: "CONSUMABLES", id: 2, selected: false, selectedSubCategories: []},
                        {name: "FIXED_ASSETS", id: 3, selected: false, selectedSubCategories: []},
                        {name :"SEMI_FINISHED" , id : 4 , selected: false , selectedSubCategories : []},
                        {name : "ALL" , id : 5 , selected: true , selectedSubCategories : []}
                    ];
                    $scope.selectedCategories = {name : "test " , selectedValues :[] , selectedSubCategories : [] };
//                    $scope.checkPendingDayClose($scope.unitData, function() {
//                        getInitiatedDayCloseEvent($scope.unitData.id);
//                    });

                    $scope.selectedCategoryForDayClose = [];
                    getInitiatedDayCloseEvent($scope.unitData.id);
                    $scope.showPreview = previewModalService.showPreview;
                    $scope.isSubmit = false;
                    $scope.isAllListType = false;
                    $scope.selectedCategory = null;
                    $scope.selectedSubCategories = [];
                    $scope.showGrid = false;
                    $scope.dayClosePending = false;
                };

                };

            $scope.setProductMap = function () {
                $scope.productMap = {};
                var prodsList = appUtil.getScmProductDetails();
                angular.forEach(prodsList, function (prod) {
                    $scope.productMap[prod.productId] = prod;
                });
            };

            $scope.setExpiryDate = function (selectedExpiry, expiryTime, item) {
                var parsedDate = new Date(selectedExpiry);
                parsedDate.setHours(parseInt(expiryTime));
                parsedDate.setMinutes(0);
                parsedDate.setSeconds(0);
                item.selectedExpiryDate = parsedDate;
            };

                $scope.idToString = function (arr){
                    var temp = angular.copy(arr);
                    for(var i = 0;i<temp.length;i++){
                        if(temp[i].subCategoryId == -1){
                            continue;
                        }
                        temp[i] = String(temp[i].subCategoryId);
                    }
                    return temp;
                }


                $scope.getSelectedSubCategories = function (){
                    if($scope.isAllListType == false || $scope.isAllListType == null){
                        return [];
                    }
                    var subCat = [];
                    for(var i = 0 ;i<$scope.categories.length;i++){
                        if($scope.categories[i].name === 'ALL'){
                            continue;
                        }
                        subCat = subCat.concat($scope.categories[i].selectedSubCategories);
                    }
                    $scope.selectedSubCategories = subCat;
                    return $scope.selectedSubCategories;

                }

                $scope.onSubCategorySelect = function (categoryId){
                    var arr = $scope.categories[categoryId - 1].selectedSubCategories;
                    if(arr.indexOf('-1') != -1){
                        $scope.categories[categoryId-1].selectedSubCategories = $scope.idToString($scope.subCategoriesMap.get(parseInt(categoryId)));
                    }
                }

                function getCategoryToSubCategoryMap(categories){
                    var subCategoryMap = new Map();
                    for(var i = 0;i<categories.length ; i++){
                        subCategoryMap.set(categories[i].categoryId,categories[i].subCategories);
                    }
                    return subCategoryMap;
                }

                function settleRejectedPendingGr(unitId, userId) {
                    var promise = $http({
                        method: "POST",
                        url: apiJson.urls.warehouseClosing.setlleRejectedGR + "/" + unitId + "/" + userId
                    }).then(function (response) {
                        console.log("response ", response);
                        if (!appUtil.isEmptyObject(response) && !appUtil.isEmptyObject(response.data)) {
                            console.log("response ", response);
                        }
                        return response.data;
                    }, function (error) {
                        console.log("Got error while setling rejected pending GR", error);
                    });
                    return promise;
                }

                $scope.gridOptions = {
                     expandableRowTemplate: 'expandableRowTemplate.html',
                     expandableRowScope: {
                           subGridVariable: 'subGridScopeVariable'
                         },
                     showColumnFooter: true,
                     enableGridMenu: true,
                     exporterExcelFilename: 'download.xlsx',
                     exporterExcelSheetName: 'Sheet1',
                     enableColumnMenus: true,
                     saveFocus: false,
                     enableRowSelection: true,
                     enableFiltering: true,
                     saveScroll: true,
                     enableSelectAll: true,
                     multiSelect: true,
                     enableColumnResizing: true,
                     exporterMenuPdf : false,
                     exporterMenuExcel : true,
                     fastWatch: true,
                     onRegisterApi: function(gridApi){
                        $scope.gridApi = gridApi;
                        gridApi.expandable.on.rowExpandedStateChanged($scope,function(row){
                            if (row.isExpanded) {
                                $scope.gridApi.expandable.collapseAllRows();
                                        row.isExpanded = true;
                            }
                            $scope.gridOptions.expandableRowHeight = row.entity.subGridOptions.data.length * row.height + row.height + 2 * row.grid.headerHeight;
                        });
                     },
                 };
                $scope.gridOptions.columnDefs = [
                { name: 'assetName', displayName: 'Asset Name'},
                { name: 'subData.length' , displayName: 'Qty', aggregationType: uiGridConstants.aggregationTypes.sum},
                { name: 'productId', displayName: 'Product Id'},
                { name: 'subCategory', displayName: 'sub-category'}
                ]

                $scope.expandAllRows = function() {
                  $scope.gridApi.expandable.expandAllRows();
                };
                $scope.collapseAllRows = function() {
                  $scope.gridApi.expandable.collapseAllRows();
                };

                $scope.checkPendingDayClose = function(unit, callback){
                    $scope.showInitiate = false;
                    $http({
                        method: 'GET',
                        url: apiJson.urls.warehouseClosing.checkFixedAssetDayClose,
                        params: {
                            id:unit.id
                        }
                    }).then(function success(response) {
                        if(response.data != null){
                            $scope.blockDaily = response.data.blockDaily;
                            $scope.blockWeekly = response.data.blockWeekly;
                            $scope.assets = response.data.availableAssetsDaily;
                            if($scope.blockWeekly){
                                $scope.assets = response.data.availableAssetsWeekly;
                            }
                            if($scope.blockDaily || $scope.blockWeekly){
                                $scope.dayClosePending = true;
                                $scope.LastDayCloseDate = appUtil.formatDate(response.data.lastDailyDayClose, 'dd-MM-yyyy');
                                if($scope.blockWeekly){
                                  $scope.LastDayCloseDate = appUtil.formatDate(response.data.lastWeeklyDayClose, 'dd-MM-yyyy');
                                }
                            }else{
                                $scope.showInitiate = true;
                            }
                            var today = new Date();
                            if(today.getDay() == 0 || today.getDay() == 5 || today.getDay() == 6){
                                $scope.dayClosePending = false;
                                $scope.showInitiate = true;
                            }
                            if($scope.assets != null){
                                    var data = $scope.assets;
                                    $scope.totalAssets = data.length;
                                    var subData = {};
                                    var productSet = {};
                                    for(var i = 0; i < data.length; i++){
                                         var product =  {
                                            productId: data[i].productId,
                                            assetName : data[i].assetName,
                                            subCategory : data[i].subCategory
                                         }
                                         if(subData[data[i].productId] == null){
                                            subData[data[i].productId] = [];
                                        }
                                          subData[data[i].productId].push(data[i]);
                                          product['subData'] = subData[data[i].productId];
                                          productSet[data[i].productId] = product;
                                    }
                                   data = Object.values(productSet);
                                    for(i = 0; i < data.length; i++){
                                        data[i].subGridOptions = {
                                                columnDefs: [
                                                    {field: 'assetId', name: 'asset-id', displayName: 'Asset Id'},
                                                    {field: 'skuId', name: 'sku-id', displayName: 'SKU Id'},
                                                    {field: 'assetStatus', name: 'asset-status', displayName: 'Asset Status'},
                                                    {field: 'assetTag', name: 'tag-value', displayName: 'Tag Value'}
                                                ],
                                            data: data[i].subData,
                                            };
                                    }
                                    $scope.gridOptions.data = data;
                                    callback();
                            }
                        }else{
                            $scope.dayClosePending = false;
                            callback();
                        }


                    });
                }

                $scope.showAssetGrid= function(){
                    $scope.showGrid = !$scope.showGrid;
                }
                $scope.createDayCloseEvent = function (unitId, userId) {
                    if($scope.isAllListType && $scope.selectedCategories.selectedValues.length === 0 ){
                        $alertService.alert("No Category Selected","Please Select AtLeast One Category For Full Inventory Audit Day Close",true);
                        return;
                    }
                    if($scope.isAllListType && $scope.getSelectedSubCategories().length === 0){
                        $alertService.alert("No Sub Category Selected","Please Select AtLeast One Sub Category For Full Inventory Audit Day Close",true);
                        return;
                    }

                    if($scope.isAllListType){
                        $scope.isAccessToAuditDayClose = true;
                    }
                    $scope.checkVarianceAcknowledgement(unitId, userId);
                    // $scope.getPendingGrs(unitId, userId);
                };

            $scope.closeReceivingModal = function () {
                $timeout(function () {
                    $('#pendingGrModal').val('').trigger('change');
                });
                if($scope.dayCloseBlocked == true && $scope.blockOnPendingGrs == true){
                    $toastService.create("can't Start Day Close as Grs Are Pending");
                }else if($scope.varianceBlocking){
                    $toastService.create("can't Start Day Close as Variance Acknol");
                }else{
                    startDayCloseEvent($scope.unitData.id, $scope.currentUser.userId);
                }

            }

            function openReceivingModal() {
                $timeout(function () {
                    angular.element('#openGrModal').trigger('click');
                });
            }

            $scope.checkVarianceAcknowledgement = function (unitId,userId) {
                $http.get(apiJson.urls.stockManagement.checkVarianceAcknowledgement+"?id="+unitId)
                    .success(function(response){
                        $scope.varianceBlocking = response.varianceBlocking;
                        $scope.getPendingGrs(unitId,userId);
                    }).error(function(response){
                    $scope.varianceBlocking = response.varianceBlocking;
                    $scope.getPendingGrs(unitId,userId);
                    console.log("Could not get acknowledgement data");
                });
            }

            $scope.getPendingGrs = function (unitId, userId) {
                $http({
                    method: "GET",
                    url: apiJson.urls.goodsReceivedManagement.pendingGrs,
                    params: {
                        unitId: $scope.unitData.id,
                        fetchRejected : true
                    }
                }).then(function (response) {
                    if (!appUtil.isEmptyObject(response) && !appUtil.isEmptyObject(response.data)) {
                        $scope.pendingGrs = response.data;

                        if ($scope.pendingGrs.length > 0 || $scope.varianceBlocking) {
                            var showModal =  checkForBlockedGrs($scope.pendingGrs);
                            if(showModal == true || $scope.varianceBlocking){
                                openReceivingModal();
                            }else{
                                startDayCloseEvent(unitId, userId);
                            }
                        } else {
                            startDayCloseEvent(unitId, userId);
                        }
                    }else{
                        if(!$scope.varianceBlocking){
                            startDayCloseEvent(unitId, userId);
                        }else {
                            $scope.dayCloseBlocked = true;
                            openReceivingModal();
                        }
                        // startDayCloseEvent(unitId, userId);
                    }
                }, function (error) {
                    console.log("Got error while getting Pending Grs");
                });
            };



            function isIntraRegionGr(gr){
                var generationUnitRegion = appUtil.findUnitDetail(gr.generationUnitId.id).region;
                var generatedForUnitRegion = appUtil.findUnitDetail(gr.generatedForUnitId.id).region;
                return generatedForUnitRegion == generationUnitRegion;
            }

            function checkForBlockedGrs(grs){
                $scope.blockedGrs = [];
                var showModal = false;
                $scope.dayCloseBlocked = false;
                for(var i in grs){
                    var blockPeriod = isIntraRegionGr(grs[i]) ? 3 : 8;
                    var grDate = new Date(grs[i].generationTime);
                    var currentDate = new Date();
                    //grDate.setHours(0,0,0,0);
                    //currentDate.setHours(0,0,0,0);
                    var dayDiff = (currentDate - grDate)/(1000*60*60*24);
                    if(dayDiff >= blockPeriod){
                        grs[i].blocked = true;
                        $scope.dayCloseBlocked = true;
                        showModal = true;
                        $scope.blockedGrs.push(grs[i]);
                    }else{
                        grs[i].blockingInDays = blockPeriod - Math.floor(dayDiff);
                        if(grs[i].blockingInDays == 1){
                            showModal = true;
                            $scope.blockedGrs.push(grs[i]);
                        }
                        grs[i].blocked = false;
                    }
                }

                return showModal;
            }




            function startDayCloseEvent(unitId, userId) {
                $alertService.confirm("Are you sure?",
                    "Please ensure you have closed all your transactions before staring day close process",
                    function (result) {
                        if (result) {
                            $http({
                                method: "POST",
                                url: apiJson.urls.warehouseClosing.dayClose + "/" + unitId + "/" + userId + "?isAllListType=" + $scope.isAllListType,
                                    data : $scope.getSelectedSubCategories()
                                }).then(function (response) {
                                    if (!appUtil.isEmptyObject(response) && !appUtil.isEmptyObject(response.data)) {
                                        $rootScope.restrictAll = true;
                                        $scope.dayCloseEvent = response.data;
                                        $scope.dayCloseInitiated = true;
                                        $scope.ackReceivings();
                                    }
                                }, function (error) {
                                    console.log("Got error while getting day close");
                                });
                            }
                        }
                    );
                }


                $scope.downloadInventorySheet = function () {
                    if($scope.isAllListType && $scope.selectedCategories.selectedValues.length === 0 && $scope.selectedCategoryForDayClose.length === 0){
                        $alertService.alert("No Category Selected","Please Select AtLeast One Category For Download",true);
                        return;
                    }
                    if($scope.isAllListType && $scope.getSelectedSubCategories().length === 0  && $scope.selectedCategoryForDayClose.length === 0){
                        $alertService.alert("No Sub Category Selected","Please Select AtLeast One Sub Category For Download",true);
                        return;
                    }
                    $http({
                        url: apiJson.urls.warehouseClosing.downloadList + "?unitId="+
                            $scope.unitData.id+"&isAllListType="+$scope.isAllListType,
                        method: 'POST',
                        responseType: 'arraybuffer',
                        //data: $scope.selectedCategories.selectedValues,
                        data: $scope.getSelectedSubCategories(),
                        headers: {
                            'Content-type': 'application/json',
                            'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        }
                    }).success(function (data) {
                        var unitName = $scope.unitData.name.split(' ').join('_');
                        var fileName = "Inventory_List_" + unitName + "_" + appUtil.formatDate(Date.now(), "dd-MM-yyyy-hh-mm-ss") + ".xls";
                        var blob = new Blob([data], {
                            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        }, fileName);
                        saveAs(blob, fileName);
                    }).error(function (err) {
                        console.log("Error during getting data", err);
                    });
                };


                $scope.cancelDayCloseEvent = function (eventId, userId) {
                    $alertService.confirm("Are you sure?",
                        "Please be sure of cancelling day close process", function (result) {
                            if (result) {
                                $http({
                                    method: "POST",
                                    url: apiJson.urls.warehouseClosing.cancelDayClose + "/" + eventId + "/" + userId
                                }).then(function (response) {
                                    if (response.data) {
                                        $toastService.create("Day Close cancelled successfully", function () {
                                            $rootScope.restrictAll = false;
                                            $scope.init();
                                        });
                                    } else {
                                        $toastService.create("Day Close cancellation failed. Please try again!");
                                    }
                                }, function (error) {
                                    console.log("Got error while getting day close");
                                });
                            }
                        }
                    );
                };


                $scope.ackInventorySummary = function (response) {
                    $rootScope.showSpinner = true;
                    $scope.wizardStep = 10;
                    if (!appUtil.isEmptyObject(response)) {
                        $scope.correctInventory = response;
                        setProducts();
                        $scope.setPage(1);
                    } else {
                        $scope.correctInventory = [];
                    }
                    $scope.setMaxDateForSku($scope.correctInventory);
                    $rootScope.showSpinner = false;
                };

                function setProducts() {
                    var skuMap = {};
                    var packagingMap = appUtil.getPackagingMap();
                    Object.values(appUtil.getSkuProductMap()).concat().forEach(function (skuList) {
                        for (var i in skuList) {
                            var sku = angular.copy(skuList[i]);
                            sku.skuPackagings = sku.skuPackagings.map(function (pkg) {
                                return packagingMap[pkg.packagingId];
                            });
                            skuMap[sku.skuId] = sku;
                        }
                    });

                    $scope.correctInventory.forEach(function (item) {
                        if (item.skuId == undefined || skuMap[item.skuId] == undefined) {
                            console.log("Found no entry here", item, item.skuId);
                        }
                        item.name = skuMap[item.skuId].skuName;
                        item.packagings = angular.copy(skuMap[item.skuId].skuPackagings);
                    });

                }

                $scope.editStock = function (item) {
                    var actual = item.stockValue;
                    item.stockValue = calculateVariance(item.packagings);
                    item.variance = item.expectedValue - item.stockValue;
                    item.varianceCost = (item.variance * item.unitPrice).toFixed(2);
                    if (item.stockValue != actual) {
                        item.edited = true;
                    }
                };

                $scope.removeEditedStock = function (item, index) {
                    item.packagings[index].value = undefined;
                    $scope.editStock(item);
                };


                $scope.setPage = function (page) {
                    if ($scope.pager == undefined) {
                        $scope.pager = pagerService.getPager($scope.correctInventory.length, page);
                        $scope.page = page;
                    } else {
                        if (page < 1 || page > $scope.pager.totalPages) {
                            return;
                        }
                        // get pager object from service
                        $scope.pager = pagerService.getPager($scope.correctInventory.length, page);
                        $scope.page = $scope.pager.currentPage;
                        // get current page of items
                    }
                    $scope.correctInventoryItems = $scope.correctInventory.slice($scope.pager.startIndex, $scope.pager.endIndex + 1);
                };

                function validateProp(list, prop) {
                    var flag = true;
                    if (list.length > 0) {
                        flag = list.filter(function (sku) {
                            return sku.hasOwnProperty(prop) && appUtil.isEmptyObject(sku[prop]);
                        }).length == 0;
                    }
                    return flag;
                }

                function transformInventory(inventoryList) {
                    var returnList = inventoryList.map(function (item) {
                        return {
                            skuId: item.skuId,
                            name: item.name,
                            qty: item.stockValue,
                            description: item.varianceReason,
                            expiryDate: (item.stockValue > 0 && item.variance < 0 && item.categoryId === 4) ? item.selectedExpiryDate : null
                        };

                    });
                    return returnList;
                }


                function printErrors(errors) {
                    var data = null;
                    if (errors != null) {
                        data = 'Errors : ';
                        for (var index in errors) {
                            data = data + errors[index].error + ' - ' + 'Sku name : ' + errors[index].name + ',';

                        }
                    }
                    return data;

                }

                function addError(array, index, error) {
                    array.push(getErrorMessage(index, error));
                }

                function getErrorMessage(name, error) {
                    var obj = {
                        name: name,
                        error: error
                    };
                    return obj;
                }

                function validateVarianceReason(error, inventory) {
                    for (var i in inventory) {
                        if (inventory[i].variance != 0 && inventory[i].varianceReason == null) {
                            addError(error, inventory[i].name, "please fill variance reason ");

                        }
                    }
                    return error;
                }

            $scope.validateUpdatedExpiryDates = function () {
                var missingExpiryDates = [];
                angular.forEach($scope.inventoryList, function (inventoryListItem) {
                    if (inventoryListItem.stockValue > 0 && inventoryListItem.variance < 0 && inventoryListItem.categoryId === 4) {
                        if (inventoryListItem.selectedExpiryDate === undefined || inventoryListItem.selectedExpiryDate === null) {
                            missingExpiryDates.push(inventoryListItem.name);
                        }
                    }
                });
                if (missingExpiryDates.length > 0) {
                    $toastService.create("Please Update the Expiry Date Of the Sku's " + missingExpiryDates.join(","));
                    return false;
                }
                return true;
            };


                $scope.showDayClosePreview = function (){
                        if (!$scope.validateUpdatedExpiryDates()) {
                            return;
                        }
                        var viewDetailModal = Popeye.openModal({
                            templateUrl: "dayClosePreview.html",
                            controller: "dayClosePreviewModalCtrl",
                            resolve: {
                                inventoryList: function () {
                                    return $scope.inventoryList;
                                },
                                dayCloseEvent : function (){
                                    return $scope.dayCloseEvent;
                                }
                            },
                            modalClass: 'custom-modal',
                            click: false,
                            keyboard: false
                        });
                        viewDetailModal.closed.then(function (check) {
                            if(check){
                                $scope.isSubmit = true;
                            }
                        });

                }


                $scope.submitInventory = function (inventory) {
                    console.log(inventory)
                    var error = [];
                    error = validateVarianceReason(error, inventory);
                    if (error.length > 0) {
                        alert(printErrors(error));
                        return;

                    }


                    if (validateProp($scope.inventoryList, 'stockValue')) {
                        $http({
                            method: "POST",
                            url: apiJson.urls.warehouseClosing.submitInventory,
                            data: {
                                id: $scope.currentUser.userId,
                                event: $scope.dayCloseEvent.id,
                                items: transformInventory(inventory),
                                dayCloseTxnItems : $scope.getDayCloseTxnItems(inventory)
                            }
                        }).then(function (response) {
                            if (response.data) {
                                $toastService.create("Inventory submitted");
                                showSummary();
                            }
                        }, function (error) {
                            $alertService.alert(error.data.errorType, error.data.errorMessage, true);
                            console.log("Got error while getting day close");
                            console.log(error);
                        });
                    } else {
                        $alertService.alert("Error! Incorrect Submission...",
                            "Please enter correct value for SKUs. No item can be left with blank values");
                    }
                };

                $scope.getDayCloseTxnItems = function (inventoryItems) {
                    var result = [];
                    angular.forEach(inventoryItems, function (item) {
                       if (item.stockValue > 0 && item.variance < 0 && item.categoryId === 4) {
                           var obj = {
                               "keyId" : item.skuId,
                               "expiryDate" : item.selectedExpiryDate
                           };
                           result.push(obj);
                       }
                    });
                    return result;
                };

                $scope.submitDayClose = function () {

                    var editedInventory = $scope.correctInventory.filter(function (item) {
                        return item.edited;
                    });
                    var error = [];
                    error = validateVarianceReason(error, editedInventory);
                    if (error.length > 0) {
                        alert(printErrors(error));
                        return;

                    }


                    if (validateProp(editedInventory, 'stockValue')) {
                        $alertService.confirm("Are your sure you want to close Day?", "", function (result) {
                            if (result) {
                                $http({
                                    method: "POST",
                                    url: apiJson.urls.warehouseClosing.submitDayClose,
                                    data: {
                                        userId: $scope.currentUser.userId,
                                        eventId: $scope.dayCloseEvent.id,
                                        items: editedInventory,
                                        dayCloseTxnItems : $scope.getDayCloseTxnItems(editedInventory)
                                    }
                                }).then(function (response) {
                                    if (response.data) {
                                        $toastService.create("Day Closed successfully");
                                        $scope.init();
                                    } else {
                                        $toastService.create("Could not close day for Unit. Try again later!");
                                    }
                                }, function (error) {
                                    $alertService.alert(!appUtil.isEmptyObject(error.data.errorType) ? error.data.errorType : error.data.errorTitle ,
                                        !appUtil.isEmptyObject(error.data.errorMessage) ? error.data.errorMessage : error.data.errorMsg , function () {
                                        $scope.init();
                                    }, true);
                                });
                            }
                        });
                    } else {
                        $alertService.alert("Negative Items in the list",
                            "You have not settled negative inventory items, <br> Please close them before moving forward");
                    }

                };


                function ackTxns(obj, type, callback) {
                    $http({
                        method: "POST",
                        url: apiJson.urls.warehouseClosing.ackTrxns,
                        data: {
                            id: $scope.currentUser.userId,
                            event: $scope.dayCloseEvent.id,
                            type: type,
                            items: obj,
                            unitId:$scope.unitData.id,
                            eventIds:$scope.eventIds
                        }
                    }).then(function (response) {
                        if (response.data) {
                            callback(response);
                        }
                    }, function (error) {
                        $toastService.create("Could not acknowledge transactions. Please contact support team");
                        console.log("Got error while getting day close");
                    });
                }

                $scope.ackAllReceivings = function (receivings) {
                    ackTxns(receivings, "RECEIVINGS", function (response) {
                        if (response.data) {
                            $scope.ackBookings();
                        }
                    });
                };

                $scope.ackAllBookings = function (bookings) {
                    ackTxns(bookings, "BOOKINGS", function (response) {
                        if (response.data) {
                            $scope.ackReverseBookings();
                        }
                    });
                };

                $scope.ackAllReverseBookings = function (reverseBookings) {
                    ackTxns(reverseBookings, "REVERSE_BOOKING", function (response) {
                        if (response.data) {
                            $scope.ackTransfers();
                        }
                    });
                };

                $scope.ackAllTransfers = function (transfers) {
                    ackTxns(transfers, "TRANSFERS", function (response) {
                        if (response.data) {
                            $scope.ackGatepasses();
                        }
                    });
                };

                $scope.ackAllGatepasses = function (txns) {
                    ackTxns(txns, "GATEPASS", function (response) {
                        if (response.data) {
                            $scope.ackInvoices();
                        }
                    });
                };

                $scope.ackAllInvoices = function (txns) {
                    ackTxns(txns, "INVOICE", function (response) {
                        if (response.data) {
                            $scope.ackReturns();
                        }
                    });
                };

                $scope.ackAllReturns = function (txns) {
                    ackTxns(txns, "GATEPASS_RETURN", function (response) {
                        if (response.data) {
                            $scope.ackWastages();
                        }
                    });
                };

                $scope.ackAllWastages = function (wastages) {
                    ackTxns(wastages, "WASTAGES", function (response) {
                        if (response.data) {
                            $scope.ackInventory();
                        }
                    });
                };

                $scope.submitConfirmation = function (inventory){
                    if (!$scope.validateUpdatedExpiryDates()) {
                        return;
                    }
                    $alertService.confirm("Are You Sure?",
                        "On clicking yes, you are accepting that you have seen the possible variance. Day close cannot be cancelled after this point.",function (isAccepted){
                        if(isAccepted){
                            $scope.submitInventory(inventory);
                        }
                    });
                }

            }
        ]
    ).controller('dayClosePreviewModalCtrl', ['$scope', 'appUtil','apiJson','$http', 'Popeye', '$toastService','$alertService',
    'inventoryList','dayCloseEvent','$rootScope' , 'pagerService',
    function ($scope, appUtil,apiJson,$http, Popeye,$toastService,$alertService,inventoryList , dayCloseEvent,$rootScope,pagerService) {


        $scope.init = function (){
              $scope.inventoryList = angular.copy(inventoryList);
              $scope.correctInventory = [];
              $scope.currentUser = appUtil.getCurrentUser();
              $scope.dayCloseEvent = dayCloseEvent;
              $scope.isSubmit = false;
              $scope.submitInventory($scope.inventoryList);
        }


        $scope.submitInventory = function (inventory) {
            console.log(inventory)
            var error = [];
            error = validateVarianceReason(error, inventory);
            if (error.length > 0) {
                $alertService.alert("Errors : ",printErrors(error),function (){
                    $scope.modalClose();
                });
                return;

            }


            if (validateProp($scope.inventoryList, 'stockValue')) {
                $http({
                    method: "POST",
                    url: apiJson.urls.warehouseClosing.previewNegativeStocks,
                    data: {
                        id: $scope.currentUser.userId,
                        event: $scope.dayCloseEvent.id,
                        items: transformInventory($scope.inventoryList)
                    }
                }).then(function (response) {
                    if (response.data) {
                        if (!appUtil.isEmptyObject(response) && !appUtil.isEmptyObject(response.data)) {
                            $scope.ackInventorySummary(response.data);
                        } else {
                            $scope.ackInventorySummary();
                        }
                    }
                }, function (error) {
                    $alertService.alert(error.data.errorType, error.data.errorMessage, function (){
                        $scope.modalClose();
                    },true);
                    console.log("Got error while getting day close");
                    console.log(error);
                });
            } else {
                $alertService.alert("Error! Incorrect Submission...",
                    "Please enter correct value for SKUs. No item can be left with blank values",function (){
                        $scope.modalClose();
                    });

            }
        };

        function validateProp(list, prop) {
            var flag = true;
            if (list.length > 0) {
                flag = list.filter(function (sku) {
                    return sku.hasOwnProperty(prop) && appUtil.isEmptyObject(sku[prop]);
                }).length == 0;
            }
            return flag;
        }

        function transformInventory(inventoryList) {
            var returnList = inventoryList.map(function (item) {
                return {
                    skuId: item.skuId,
                    name: item.name,
                    qty: item.stockValue,
                    description: item.varianceReason
                };

            });
            return returnList;
        }


        function printErrors(errors) {
            var data = null;
            if (errors != null) {
                data = 'Errors : ';
                for (var index in errors) {
                    data = data + errors[index].error + ' - ' + 'Sku name : ' + errors[index].name + ',';

                }
            }
            return data;

        }

        function addError(array, index, error) {
            array.push(getErrorMessage(index, error));
        }

        function getErrorMessage(name, error) {
            var obj = {
                name: name,
                error: error
            };
            return obj;
        }

        function validateVarianceReason(error, inventory) {
            for (var i in inventory) {
                if (inventory[i].variance != 0 && inventory[i].varianceReason == null) {
                    addError(error, inventory[i].name, "please fill variance reason ")

                }
            }
            return error;
        }

        $scope.ackInventorySummary = function (response) {
            $rootScope.showSpinner = true;
            if (!appUtil.isEmptyObject(response)) {
                $scope.correctInventory = response;
                setProducts();
                $scope.setPage(1);
            } else {
                $scope.correctInventory = [];
            }
            $rootScope.showSpinner = false;
        };

        function setProducts() {
            var skuMap = {};
            var packagingMap = appUtil.getPackagingMap();
            Object.values(appUtil.getSkuProductMap()).concat().forEach(function (skuList) {
                for (var i in skuList) {
                    var sku = angular.copy(skuList[i]);
                    sku.skuPackagings = sku.skuPackagings.map(function (pkg) {
                        return packagingMap[pkg.packagingId];
                    });
                    skuMap[sku.skuId] = sku;
                }
            });

            $scope.correctInventory.forEach(function (item) {
                if (item.skuId == undefined || skuMap[item.skuId] == undefined) {
                    console.log("Found no entry here", item, item.skuId);
                }
                item.name = skuMap[item.skuId].skuName;
                item.packagings = angular.copy(skuMap[item.skuId].skuPackagings);
            });

        }


        $scope.setPage = function (page) {
            if ($scope.pager == undefined) {
                $scope.pager = pagerService.getPager($scope.correctInventory.length, page);
                $scope.page = page;
            } else {
                if (page < 1 || page > $scope.pager.totalPages) {
                    return;
                }
                // get pager object from service
                $scope.pager = pagerService.getPager($scope.correctInventory.length, page);
                $scope.page = $scope.pager.currentPage;
                // get current page of items
            }
            $scope.correctInventoryItems = $scope.correctInventory.slice($scope.pager.startIndex, $scope.pager.endIndex + 1);
        };

        $scope.modalClose = function (){
            Popeye.closeCurrentModal($scope.isSubmit);
        }

        $scope.acceptVariance = function (){
            $scope.isSubmit = true;
            $scope.modalClose();
        }

        $scope.getVarianceCost = function (closing,unitPrice){
            return Math.abs((closing.toFixed(2)*unitPrice)).toFixed(2);
        }

    }]);


