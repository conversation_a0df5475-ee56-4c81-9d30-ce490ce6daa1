// Virtual Scroll Directive for Large Lists Performance (ES5 Compatible)
angular.module('scmApp').directive('virtualScroll', function() {
    return {
        restrict: 'A',
        scope: {
            items: '=virtualScroll',
            itemHeight: '@',
            containerHeight: '@'
        },
        template: '<div class="virtual-scroll-container" style="height: {{containerHeight}}px; overflow-y: auto; border: 1px solid #ddd;">' +
                    '<div class="virtual-scroll-spacer-before" style="height: {{spacerBefore}}px;"></div>' +
                    '<div class="virtual-scroll-content" ng-transclude></div>' +
                    '<div class="virtual-scroll-spacer-after" style="height: {{spacerAfter}}px;"></div>' +
                  '</div>',
        transclude: true,
        link: function(scope, element) {
            var itemHeight = parseInt(scope.itemHeight) || 60;
            var containerHeight = parseInt(scope.containerHeight) || 500;
            var visibleCount = Math.ceil(containerHeight / itemHeight) + 5; // Extra buffer for smooth scrolling

            scope.spacerBefore = 0;
            scope.spacerAfter = 0;

            var container = element.find('.virtual-scroll-container')[0];
            var content = element.find('.virtual-scroll-content')[0];
            var isUpdating = false;

            function updateVisibleItems() {
                if (isUpdating || !scope.items || scope.items.length === 0) {
                    scope.spacerBefore = 0;
                    scope.spacerAfter = 0;
                    return;
                }

                isUpdating = true;

                var scrollTop = container.scrollTop;
                var startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - 2); // Small buffer above
                var endIndex = Math.min(startIndex + visibleCount, scope.items.length);

                scope.spacerBefore = startIndex * itemHeight;
                scope.spacerAfter = (scope.items.length - endIndex) * itemHeight;

                // Apply changes safely
                if (!scope.$$phase) {
                    scope.$apply();
                }

                isUpdating = false;
            }

            // Throttled scroll handler for better performance
            var scrollTimeout;
            function onScroll() {
                if (scrollTimeout) {
                    clearTimeout(scrollTimeout);
                }
                scrollTimeout = setTimeout(updateVisibleItems, 16); // ~60fps
            }

            container.addEventListener('scroll', onScroll);

            scope.$watch('items', function(newItems) {
                if (newItems) {
                    setTimeout(updateVisibleItems, 0);
                }
            });

            // Cleanup
            scope.$on('$destroy', function() {
                container.removeEventListener('scroll', onScroll);
                if (scrollTimeout) {
                    clearTimeout(scrollTimeout);
                }
            });

            // Initial load
            setTimeout(updateVisibleItems, 100);
        }
    };
});
